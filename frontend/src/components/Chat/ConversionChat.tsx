import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  Box,
  TextField,
  IconButton,
  Typography,
  Paper,
  CircularProgress,
  Divider,
  Avatar,
  Fade,
  Tooltip,
  useTheme,
  Alert,
  Chip,
  ToggleButtonGroup,
  ToggleButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button
} from '@mui/material';
import {
  Send as SendIcon,
  FileCopy as CopyIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CheckIcon from '@mui/icons-material/Check';
import ThumbUpAltIcon from '@mui/icons-material/ThumbUpAlt';
import ThumbUpOutlinedIcon from '@mui/icons-material/ThumbUpOutlined';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import PersonIcon from '@mui/icons-material/Person';
import FeedbackIcon from '@mui/icons-material/Feedback';
import CalculateIcon from '@mui/icons-material/Calculate';
import TableChartIcon from '@mui/icons-material/TableChart';
import EmailIcon from '@mui/icons-material/Email';
import ChatIcon from '@mui/icons-material/Chat';
import InfoIcon from '@mui/icons-material/Info';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import { v4 as uuidv4 } from 'uuid';
import { convertMarkdownTableToExcel, convertJsonTableToExcel } from '../../utils/excelUtils';
import {
  parseMarkdownTable,
  containsMarkdownTable,
  markdownTableToExcelHTML,
  validateAndCleanTableContent,
  tableToExcelHTML,
  tableToTSV,
  ParsedTable,
  StreamingTableParser
} from '../../utils/tableUtils';
import { streamingService, StreamingChunk } from '../../services/streamingService';
import { useAuth } from '../../contexts/AuthContext';
import EmailDialog from './EmailDialog';
import { useLanguage } from '../../contexts/LanguageContext';
import { saveMessages } from '../../services/chatStorage';
import {
  Message as ChatMessage,
  Function as FunctionType,
  ConversionResult,
  TableData
} from '../../types/chat';
import {
  ChatSession
} from '../../types/chatSession';
import {
  getSession,
  getActiveSession,
  saveMessage,
  createSession,
  getOrCreateActiveSession
} from '../../services/chatSessionStorage';

// Function to get the icon component based on the icon name

// Function to get the icon component based on the icon name
const getIconForFunction = (iconName: string) => {
  switch(iconName) {
    case 'chat':
      return <ChatIcon />;
    case 'calculate':
      return <CalculateIcon />;
    case 'table_chart':
      return <TableChartIcon />;
    default:
      return <SmartToyIcon />;
  }
};

// Add a function to check if a message is a welcome message
const isWelcomeMessage = (message: ChatMessage): boolean => {
  return message.type === 'assistant' &&
    (message.content.includes('欢迎使用') ||
     message.content.includes('welcome') ||
     message.content.includes('Welcome') ||
     message.content.includes('欢迎来到'));
};

// Check if the current state is the initial welcome state (only welcome message)
const isInitialState = (messages: ChatMessage[]): boolean => {
  return messages.length === 1 && isWelcomeMessage(messages[0]);
};

// Enhanced parsing function to handle both structured and markdown table formats
const parseConvertedContent = (content: string): { hasTable: boolean; tableContent: string; cleanContent: string; tableData?: any } => {
  console.log('parseConvertedContent: Starting parse with content length:', content.length);
  console.log('parseConvertedContent: Content preview:', content.substring(0, 200) + (content.length > 200 ? '...' : ''));

  try {
    // First check for converted_content tags
    const convertedContentMatch = content.match(/<converted_content>([\s\S]*?)<\/converted_content>/);

    if (convertedContentMatch) {
      const convertedText = convertedContentMatch[1].trim();
      const cleanContent = content.replace(/<converted_content>[\s\S]*?<\/converted_content>/, '').trim();
      console.log('parseConvertedContent: Found converted_content tags, text length:', convertedText.length);

      // Check for compact streaming table format first
      const tableStreamMatch = convertedText.match(/<table_stream>([\s\S]*?)<\/table_stream>/);
      if (tableStreamMatch) {
        console.log('parseConvertedContent: Found table_stream in converted_content');
        try {
          const tableContent = tableStreamMatch[1].trim();
          const remainingContent = convertedText.replace(/<table_stream>[\s\S]*?<\/table_stream>/, '').trim();
          console.log('parseConvertedContent: Extracted table content length:', tableContent.length);
          console.log('parseConvertedContent: Table content:', tableContent);

          // Parse the compact table format with enhanced validation using robust delimiter detection
          const lines = tableContent.split('\n').filter(line => line.trim());
          console.log('parseConvertedContent: Split into lines:', {
            totalLines: lines.length,
            lines: lines
          });

          if (lines.length >= 2) {
            const headerLine = lines[0];
            let headers: string[] = [];
            let delimiter = '|'; // Default delimiter

            // Robust delimiter detection and header parsing
            if (headerLine.includes('|')) {
              // Remove outer pipes if they exist and split
              const cleanHeader = headerLine.replace(/^\||\|$/g, '');
              headers = cleanHeader.split('|').map(h => h.trim());
              delimiter = '|';
              console.log('parseConvertedContent - Pipe-delimited headers:', {
                originalLine: headerLine,
                cleanedLine: cleanHeader,
                headers: headers,
                delimiter: delimiter
              });
            } else if (headerLine.includes('\t')) {
              headers = headerLine.split('\t').map(h => h.trim());
              delimiter = '\t';
              console.log('parseConvertedContent - Tab-delimited headers:', { headers, delimiter });
            } else if (headerLine.match(/\s{2,}/)) {
              headers = headerLine.split(/\s{2,}/).map(h => h.trim());
              delimiter = 'space';
              console.log('parseConvertedContent - Space-delimited headers:', { headers, delimiter });
            } else {
              // Fallback: single header
              headers = [headerLine.trim()];
              delimiter = 'single';
              console.log('parseConvertedContent - Single header fallback:', { headers, delimiter });
            }

            console.log('parseConvertedContent - Final header parsing result:', {
              headerCount: headers.length,
              headers: headers,
              delimiter: delimiter,
              expectedCount: 6,
              isValidCount: headers.length >= 6
            });

            // Validate header count
            if (headers.length < 6) {
              console.warn('parseConvertedContent: Insufficient headers detected:', {
                got: headers.length,
                expected: 6,
                headers: headers,
                delimiter: delimiter
              });
            }

            const rows = lines.slice(1).map((line, lineIndex) => {
              let cells: string[] = [];

              // Use the same delimiter as detected for headers
              if (delimiter === '|') {
                const cleanLine = line.replace(/^\||\|$/g, '');
                cells = cleanLine.split('|').map(c => c.trim());
              } else if (delimiter === '\t') {
                cells = line.split('\t').map(c => c.trim());
              } else if (delimiter === 'space') {
                cells = line.split(/\s{2,}/).map(c => c.trim());
              } else {
                cells = [line.trim()];
              }

              console.log(`parseConvertedContent - Enhanced row ${lineIndex} logging:`, {
                rawLine: line,
                delimiter: delimiter,
                parsedCells: cells,
                cellCount: cells.length,
                expectedCellCount: headers.length,
                isCorrectCellCount: cells.length === headers.length
              });

              const row: any = {};
              headers.forEach((header, index) => {
                row[header] = cells[index] || '';
                if (index < cells.length && !cells[index]) {
                  console.warn(`parseConvertedContent: Empty cell at row ${lineIndex}, column ${index} (${header})`);
                }
              });
              return row;
            });

            const tableData = { headers, rows };
            console.log('parseConvertedContent: Successfully created table data:', {
              headerCount: tableData.headers.length,
              rowCount: tableData.rows.length,
              headers: tableData.headers,
              firstRowSample: tableData.rows[0],
              isValidStructure: tableData.headers.length >= 6 && tableData.rows.length > 0
            });

            return {
              hasTable: true,
              tableContent: '', // Will be generated from tableData
              cleanContent: cleanContent || remainingContent,
              tableData: tableData
            };
          } else {
            console.warn('parseConvertedContent: Insufficient lines in table content:', {
              lineCount: lines.length,
              required: 2,
              lines: lines
            });
          }
        } catch (error) {
          console.error('parseConvertedContent: Failed to parse streaming table data:', {
            error: error,
            errorMessage: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : undefined
          });
        }
      } else {
        console.log('parseConvertedContent: No table_stream found in converted_content');
      }

      // Check for legacy structured table data (JSON format)
      const tableDataMatch = convertedText.match(/<table_data>([\s\S]*?)<\/table_data>/);
      if (tableDataMatch) {
        try {
          const tableData = JSON.parse(tableDataMatch[1].trim());
          const remainingContent = convertedText.replace(/<table_data>[\s\S]*?<\/table_data>/, '').trim();

          console.log('parseConvertedContent: Successfully parsed legacy table data:', {
            tableData: tableData,
            hasHeaders: !!tableData.headers,
            hasRows: !!tableData.rows,
            headerCount: tableData.headers?.length || 0,
            rowCount: tableData.rows?.length || 0
          });

          return {
            hasTable: true,
            tableContent: '', // Will be generated from tableData
            cleanContent: cleanContent || remainingContent,
            tableData: tableData
          };
        } catch (error) {
          console.error('parseConvertedContent: Failed to parse legacy table_data JSON:', {
            error: error,
            errorMessage: error instanceof Error ? error.message : 'Unknown error',
            jsonContent: tableDataMatch[1].trim()
          });
        }
      }

      // Fallback to markdown table detection with cleaning
      const cleanedTableContent = validateAndCleanTableContent(convertedText);
      const hasTable = cleanedTableContent ? containsMarkdownTable(cleanedTableContent) : containsMarkdownTable(convertedText);

      console.log('parseConvertedContent: Found converted content:', {
        convertedText,
        cleanedTableContent,
        cleanContent,
        hasTable
      });

      return {
        hasTable,
        tableContent: cleanedTableContent || convertedText,
        cleanContent
      };
    }

    // If no converted_content tags, check for streaming table format in the entire content
    console.log('parseConvertedContent: Checking entire content for table_stream tags');
    const tableStreamMatch = content.match(/<table_stream>([\s\S]*?)<\/table_stream>/);
    if (tableStreamMatch) {
      console.log('parseConvertedContent: Found table_stream in entire content');
      try {
        const tableContent = tableStreamMatch[1].trim();
        const remainingContent = content.replace(/<table_stream>[\s\S]*?<\/table_stream>/, '').trim();
        console.log('parseConvertedContent (content): Extracted table content:', {
          tableContentLength: tableContent.length,
          tableContent: tableContent,
          remainingContentLength: remainingContent.length
        });

      // Parse the compact table format using robust delimiter detection
      const lines = tableContent.split('\n').filter(line => line.trim());
      if (lines.length >= 2) {
        const headerLine = lines[0];
        let headers: string[] = [];
        let delimiter = '|'; // Default delimiter

        // Robust delimiter detection and header parsing
        if (headerLine.includes('|')) {
          // Remove outer pipes if they exist and split
          const cleanHeader = headerLine.replace(/^\||\|$/g, '');
          headers = cleanHeader.split('|').map(h => h.trim());
          delimiter = '|';
        } else if (headerLine.includes('\t')) {
          headers = headerLine.split('\t').map(h => h.trim());
          delimiter = '\t';
        } else if (headerLine.match(/\s{2,}/)) {
          headers = headerLine.split(/\s{2,}/).map(h => h.trim());
          delimiter = 'space';
        } else {
          headers = [headerLine.trim()];
          delimiter = 'single';
        }

        console.log('parseConvertedContent (content) - header parsing:', {
          originalLine: headerLine,
          headers: headers,
          headerCount: headers.length,
          delimiter: delimiter
        });

        const rows = lines.slice(1).map((line, lineIndex) => {
          let cells: string[] = [];

          // Use the same delimiter as detected for headers
          if (delimiter === '|') {
            const cleanLine = line.replace(/^\||\|$/g, '');
            cells = cleanLine.split('|').map(c => c.trim());
          } else if (delimiter === '\t') {
            cells = line.split('\t').map(c => c.trim());
          } else if (delimiter === 'space') {
            cells = line.split(/\s{2,}/).map(c => c.trim());
          } else {
            cells = [line.trim()];
          }

          console.log(`parseConvertedContent (content) - row ${lineIndex}:`, {
            rawLine: line,
            delimiter: delimiter,
            parsedCells: cells,
            cellCount: cells.length,
            expectedCellCount: headers.length
          });

          const row: any = {};
          headers.forEach((header, index) => {
            row[header] = cells[index] || '';
          });
          return row;
        });

        const tableData = { headers, rows };
        console.log('Found streaming table data in content:', tableData);
        console.log('Table data headers (content):', tableData.headers);
        console.log('Table data first row (content):', tableData.rows[0]);

        return {
          hasTable: true,
          tableContent: '', // Will be generated from tableData
          cleanContent: remainingContent,
          tableData: tableData
        };
      }
    } catch (error) {
      console.error('Failed to parse streaming table data:', error);
    }
  }

    // Check for legacy structured table data (JSON format)
    console.log('parseConvertedContent: Checking for legacy table_data format');
    const tableDataMatch = content.match(/<table_data>([\s\S]*?)<\/table_data>/);
    if (tableDataMatch) {
      console.log('parseConvertedContent: Found legacy table_data tags');
      try {
        const tableDataJson = tableDataMatch[1].trim();
        const tableData = JSON.parse(tableDataJson);
        const remainingContent = content.replace(/<table_data>[\s\S]*?<\/table_data>/, '').trim();

        console.log('parseConvertedContent: Successfully parsed legacy table data:', {
          tableData: tableData,
          hasHeaders: !!tableData.headers,
          hasRows: !!tableData.rows,
          headerCount: tableData.headers?.length || 0,
          rowCount: tableData.rows?.length || 0
        });

        return {
          hasTable: true,
          tableContent: '', // Will be generated from tableData
          cleanContent: remainingContent,
          tableData: tableData
        };
      } catch (error) {
        console.error('parseConvertedContent: Failed to parse legacy table_data JSON:', {
          error: error,
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
          jsonContent: tableDataMatch[1].trim()
        });
      }
    }

    // Final fallback to markdown table detection
    console.log('parseConvertedContent: Final fallback to markdown table detection');
    const hasTable = containsMarkdownTable(content);
    
    console.log('parseConvertedContent: Final result:', {
      hasTable: hasTable,
      contentLength: content.length,
      willReturnTableContent: hasTable,
      willReturnCleanContent: !hasTable
    });
    
    return {
      hasTable,
      tableContent: hasTable ? content : '',
      cleanContent: hasTable ? '' : content
    };
  } catch (mainError) {
    console.error('parseConvertedContent: Critical error in main parsing logic:', {
      error: mainError,
      errorMessage: mainError instanceof Error ? mainError.message : 'Unknown error',
      stack: mainError instanceof Error ? mainError.stack : undefined,
      contentLength: content.length,
      contentPreview: content.substring(0, 200)
    });
    
    // Return safe fallback
    return {
      hasTable: false,
      tableContent: '',
      cleanContent: content,
      error: mainError instanceof Error ? mainError.message : 'Parse error'
    };
  }
};

interface RobustTableRendererProps {
  content: string;
  tableData?: any;
  onCopy?: (content: string) => void;
}

interface StreamingTableRendererProps {
  streamingTableData: ParsedTable | null;
  onCopy?: (content: string) => void;
}

const RobustTableRenderer: React.FC<RobustTableRendererProps> = ({ content, tableData, onCopy }) => {
  const [copyStatus, setCopyStatus] = useState<string>('');

  const handleCopy = useCallback(async (tableContent: string, structuredData?: any) => {
    try {
      let parsedTable: ParsedTable;

      // If we have structured data, use it directly
      if (structuredData && structuredData.headers && structuredData.rows) {
        parsedTable = {
          headers: structuredData.headers,
          rows: structuredData.rows,
          isValid: true
        };
      } else {
        // Parse the markdown table
        parsedTable = parseMarkdownTable(tableContent);
        if (!parsedTable.isValid) {
          throw new Error('Invalid table format');
        }
      }

      const htmlTable = tableToExcelHTML(parsedTable);
      const tsvData = tableToTSV(parsedTable);

      // Try to copy as HTML first (better for Excel), fallback to TSV
      try {
        // Create a ClipboardItem with both HTML and text formats
        const clipboardItem = new ClipboardItem({
          'text/html': new Blob([htmlTable], { type: 'text/html' }),
          'text/plain': new Blob([tsvData], { type: 'text/plain' })
        });
        await navigator.clipboard.write([clipboardItem]);
      } catch (clipboardError) {
        // Fallback to simple text copy
        await navigator.clipboard.writeText(tsvData);
      }

      setCopyStatus('已复制！');
      setTimeout(() => setCopyStatus(''), 2000);
      onCopy?.(htmlTable);
    } catch (error) {
      console.error('Failed to copy table:', error);
      setCopyStatus('复制失败');
      setTimeout(() => setCopyStatus(''), 2000);
    }
  }, [onCopy]);

  // Use the global parseConvertedContent function
  const { hasTable, tableContent, cleanContent, tableData: structuredTableData } = parseConvertedContent(content);
  const textContent = cleanContent;

  return (
    <Box>
      {textContent && (
        <Typography 
          variant="body1" 
          sx={{ 
            whiteSpace: 'pre-wrap', 
            wordBreak: 'break-word',
            mb: tableContent ? 2 : 0
          }}
        >
          {textContent}
        </Typography>
      )}
      
      {hasTable && (() => {
        let parsedTable: ParsedTable;

        // If we have structured table data, use it directly
        if (structuredTableData && structuredTableData.headers && structuredTableData.rows) {
          console.log('RobustTableRenderer - Using structured table data:', structuredTableData);
          console.log('RobustTableRenderer - Headers:', structuredTableData.headers);
          console.log('RobustTableRenderer - Header count:', structuredTableData.headers.length);
          console.log('RobustTableRenderer - First row:', structuredTableData.rows[0]);
          
          parsedTable = {
            headers: structuredTableData.headers,
            rows: structuredTableData.rows,
            isValid: true
          };
        } else if (tableContent) {
          // Parse markdown table
          parsedTable = parseMarkdownTable(tableContent);
        } else {
          return null;
        }

        if (!parsedTable.isValid || !parsedTable.headers || !parsedTable.rows) {
          console.log('RobustTableRenderer - Invalid table detected:', {
            isValid: parsedTable.isValid,
            headers: parsedTable.headers,
            rows: parsedTable.rows,
            headerCount: parsedTable.headers?.length,
            rowCount: parsedTable.rows?.length
          });
          return (
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ fontStyle: 'italic' }}
            >
              表格格式无效，显示原始内容：
              <Box component="pre" sx={{ mt: 1, fontSize: '0.875rem' }}>
                {tableContent || JSON.stringify(structuredTableData, null, 2)}
              </Box>
            </Typography>
          );
        }

        console.log('RobustTableRenderer - About to render table:', {
          headerCount: parsedTable.headers.length,
          headers: parsedTable.headers,
          rowCount: parsedTable.rows.length,
          firstRow: parsedTable.rows[0]
        });

        // Use the actual parsed table data
        const tableToRender = parsedTable;

        return (
          <Box sx={{ mt: 2 }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
              转换表格:
            </Typography>
            <TableContainer
              component={Paper}
              sx={{
                maxHeight: 400,
                backgroundColor: 'background.paper',
                border: '1px solid',
                borderColor: 'divider',
                boxShadow: 'none',
                overflowX: 'auto',
                width: '100%'
              }}
            >
              <Table stickyHeader size="small" sx={{ minWidth: 650, tableLayout: 'auto' }}>
                <TableHead>
                  <TableRow>
                    {tableToRender.headers.map((header: string, index: number) => (
                      <TableCell
                        key={index}
                        sx={{
                          fontWeight: 'bold',
                          backgroundColor: 'action.hover',
                          whiteSpace: 'nowrap'
                        }}
                      >
                        {header}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {tableToRender.rows.map((row, rowIndex: number) => (
                    <TableRow key={rowIndex}>
                      {tableToRender.headers.map((header: string, cellIndex: number) => (
                        <TableCell
                          key={cellIndex}
                          sx={{ wordBreak: 'break-word' }}
                        >
                          {row[header] || ''}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <Box sx={{ mt: 1, display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
              <Button
                size="small"
                startIcon={copyStatus === '已复制！' ? <CheckCircleIcon /> : <CopyIcon />}
                onClick={() => handleCopy(tableContent, structuredTableData)}
                variant="outlined"
                color={copyStatus === '已复制！' ? 'success' : 'primary'}
                sx={{ fontSize: '0.75rem' }}
              >
                {copyStatus || 'Copy for Excel'}
              </Button>
              <Button
                size="small"
                startIcon={<FileDownloadIcon />}
                onClick={() => {
                  try {
                    convertMarkdownTableToExcel(tableContent, `table_${new Date().getTime()}.xlsx`);
                  } catch (error) {
                    console.error('Download failed:', error);
                  }
                }}
                variant="outlined"
                color="primary"
                sx={{ fontSize: '0.75rem' }}
              >
                下载Excel
              </Button>
            </Box>
          </Box>
        );
      })()}
    </Box>
  );
};

// Streaming Table Renderer for progressive table display
const StreamingTableRenderer: React.FC<StreamingTableRendererProps> = ({ streamingTableData, onCopy }) => {
  const [copyStatus, setCopyStatus] = useState<string>('');

  const handleCopy = useCallback(async () => {
    if (!streamingTableData || !streamingTableData.isValid) return;

    try {
      const htmlTable = tableToExcelHTML(streamingTableData);
      const tsvData = tableToTSV(streamingTableData);

      // Try to copy as HTML first (better for Excel), fallback to TSV
      try {
        const clipboardItem = new ClipboardItem({
          'text/html': new Blob([htmlTable], { type: 'text/html' }),
          'text/plain': new Blob([tsvData], { type: 'text/plain' })
        });
        await navigator.clipboard.write([clipboardItem]);
      } catch (clipboardError) {
        await navigator.clipboard.writeText(tsvData);
      }

      setCopyStatus('已复制！');
      setTimeout(() => setCopyStatus(''), 2000);
      onCopy?.(htmlTable);
    } catch (error) {
      console.error('Failed to copy streaming table:', error);
      setCopyStatus('复制失败');
      setTimeout(() => setCopyStatus(''), 2000);
    }
  }, [streamingTableData, onCopy]);

  if (!streamingTableData || !streamingTableData.isValid || !streamingTableData.headers.length) {
    return (
      <Box sx={{ p: 2, bgcolor: 'action.hover', borderRadius: 1, mt: 1 }}>
        <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
          等待表格数据...
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ mt: 2 }}>
      <Typography variant="body2" color="primary" sx={{ mb: 1, fontWeight: 'medium' }}>
        📊 实时表格 ({streamingTableData.rows.length} 行)
      </Typography>

      <TableContainer
        component={Paper}
        sx={{
          maxHeight: 400,
          border: '1px solid rgba(255, 255, 255, 0.12)',
          borderRadius: 1
        }}
      >
        <Table stickyHeader size="small" sx={{ minWidth: 650, tableLayout: 'auto' }}>
          <TableHead>
            <TableRow>
              {streamingTableData.headers.map((header: string, index: number) => (
                <TableCell
                  key={index}
                  sx={{
                    fontWeight: 'bold',
                    backgroundColor: 'action.hover',
                    whiteSpace: 'nowrap'
                  }}
                >
                  {header}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {streamingTableData.rows.map((row, rowIndex: number) => (
              <TableRow key={rowIndex}>
                {streamingTableData.headers.map((header: string, cellIndex: number) => (
                  <TableCell
                    key={cellIndex}
                    sx={{ wordBreak: 'break-word' }}
                  >
                    {row[header] || ''}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Box sx={{ mt: 1, display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
        <Button
          size="small"
          startIcon={copyStatus === '已复制！' ? <CheckCircleIcon /> : <CopyIcon />}
          onClick={handleCopy}
          variant="outlined"
          color={copyStatus === '已复制！' ? 'success' : 'primary'}
          sx={{ fontSize: '0.75rem' }}
        >
          {copyStatus || 'Copy for Excel'}
        </Button>
      </Box>
    </Box>
  );
};

const ConversionChat: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [copied, setCopied] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [conversionMode, setConversionMode] = useState<'imperial-to-metric' | 'metric-to-imperial'>('imperial-to-metric');
  const [activeFunctions, setActiveFunctions] = useState<string[]>(['general']);
  const [functions, setFunctions] = useState<FunctionType[]>([]);
  const [liked, setLiked] = useState<number[]>([]);
  const [emailDialogOpen, setEmailDialogOpen] = useState(false);
  const [selectedResultData, setSelectedResultData] = useState<any>(null);
  const [emailResultType, setEmailResultType] = useState<'conversion' | 'table'>('conversion');
  const [isLoadingFunctions, setIsLoadingFunctions] = useState(true);
  const [activeSessionId, setActiveSessionId] = useState<string>('');
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null);
  const [isSessionSwitching, setIsSessionSwitching] = useState(false);
  const [isInInitialState, setIsInInitialState] = useState(true);

  // Streaming state
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingContent, setStreamingContent] = useState<string>('');
  const [streamingMessageIndex, setStreamingMessageIndex] = useState<number | null>(null);
  
  // Debouncing for streaming updates to improve performance
  const streamingUpdateTimeoutRef = useRef<number | null>(null);
  const accumulatedStreamingContentRef = useRef<string>('');

  // Progressive table parsing state
  const [streamingTableData, setStreamingTableData] = useState<ParsedTable | null>(null);
  const streamingTableParserRef = useRef<StreamingTableParser | null>(null);

  // Session cache for temporary sessions
  const sessionCache: Record<string, ChatSession> = {};

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const { user } = useAuth();
  const theme = useTheme();
  const { t, language } = useLanguage();

  // Load available functions on mount
  useEffect(() => {
    const loadFunctions = async () => {
      try {
        setIsLoadingFunctions(true);
        
        // Always use the translated default functions
        const defaultFunctions = [
          {
            id: 'general',
            name: t('steelIndustryConsultation'),
            description: t('steelIndustryConsultationDesc'),
            icon: 'chat'
          },
          {
            id: 'conversion',
            name: t('unitConversion'),
            description: t('unitConversionDesc'),
            icon: 'calculate'
          },
          {
            id: 'table',
            name: t('tabulationFunction'),
            description: t('tabulationFunctionDesc'),
            icon: 'table_chart'
          }
        ];

        // Try to load from API but always use translated names
        try {
          // Import API_BASE_URL from config
          const { API_BASE_URL } = await import('../../config');
          console.log('Using API base URL:', API_BASE_URL);

          // Handle HTTPS fallback for production servers
          let apiUrl = `${API_BASE_URL}/llm/functions`;

          // Check if we're on HTTPS and need to fallback to HTTP
          if (window.location.protocol === 'https:' &&
              (window.location.hostname === 'steelnet.ai' ||
               window.location.hostname === '************')) {
            // Try to use the proxy first
            apiUrl = `/api/llm/functions`;
            console.log('Using proxy URL for HTTPS fallback:', apiUrl);
          }

          console.log('Fetching functions from:', apiUrl);
          const response = await fetch(apiUrl);
          console.log('Functions API response status:', response.status);

          if (response.ok) {
            const data = await response.json();
            console.log('Functions loaded from API:', data);
            
            // Map API response to use translated names
            const translatedFunctions = data.map((func: any) => {
              // Find the matching default function to get the proper translation key
              const defaultFunc = defaultFunctions.find(df => df.id === func.id);
              return {
                ...func,
                name: defaultFunc?.name || func.name,
                description: defaultFunc?.description || func.description,
                icon: defaultFunc?.icon || func.icon
              };
            });
            
            setFunctions(translatedFunctions);
          } else {
            console.error('Failed to load functions from API, using defaults');
            setFunctions(defaultFunctions);
          }
        } catch (apiError) {
          console.error('Error loading functions from API:', apiError);
          setFunctions(defaultFunctions);
        }
      } catch (error) {
        console.error('Error in loadFunctions:', error);
        // Fallback to default functions with translations
        setFunctions([
          {
            id: 'general',
            name: t('steelIndustryConsultation'),
            description: t('steelIndustryConsultationDesc'),
            icon: 'chat'
          },
          {
            id: 'conversion',
            name: t('unitConversion'),
            description: t('unitConversionDesc'),
            icon: 'calculate'
          },
          {
            id: 'table',
            name: t('tabulationFunction'),
            description: t('tabulationFunctionDesc'),
            icon: 'table_chart'
          }
        ]);
      } finally {
        setIsLoadingFunctions(false);
      }
    };

    loadFunctions();
  }, [t, language]); // Add language dependency to ensure re-loading when language changes

  // Load and initialize active session on component mount
  useEffect(() => {
    const initializeActiveSession = async () => {
      try {
        setIsLoading(true);
        console.log('Initializing active session...');

        // Get or create an active session using the dedicated function
        const activeSession = await getOrCreateActiveSession();
        console.log('Retrieved active session:', activeSession);

        // Ensure messages is an array
        if (!Array.isArray(activeSession.messages)) {
          console.warn('Active session has invalid messages property, resetting to empty array');
          activeSession.messages = [];
        }

        // Update component state with session data
        setCurrentSession(activeSession);
        console.log('Setting messages state:', activeSession.messages);
        setMessages(activeSession.messages);
        setActiveSessionId(activeSession.id);

        // Check if this is the initial state (just welcome message)
        const isInitialWelcomeState = isInitialState(activeSession.messages);
        setIsInInitialState(isInitialWelcomeState);

        // Log for debugging
        console.log('Initialized active session:', activeSession.id,
                    'Initial state:', isInitialWelcomeState,
                    'Message count:', activeSession.messages.length);
      } catch (error) {
        console.error('Failed to initialize active session:', error);
        // As a fallback, display welcome message locally
        const welcomeMessage = {
          type: 'assistant' as const,
          content: t('welcomeMessageContent'),
          timestamp: new Date()
        };
        console.log('Setting fallback welcome message');
        setMessages([welcomeMessage]);
        setIsInInitialState(true);
      } finally {
        setIsLoading(false);
      }
    };

    initializeActiveSession();
  }, [t]); // Only run this once on mount (with t dependency for translations)

  // Save messages when they change
  useEffect(() => {
    if (messages.length > 0) {
      saveMessages(messages).catch(err =>
        console.error('Failed to save chat messages:', err)
      );
    }
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Focus input on load
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  // Process a user message and get AI response
  const processUserMessage = async (
    userMessage: string,
    sessionId: string,
    effectiveActiveFunctions: string[],
    userChatMessage: ChatMessage
  ) => {
    try {
      // Determine the unit direction based on the current mode
      const unitSystem = conversionMode === 'imperial-to-metric' ? 'metric' : 'imperial';

      // Import API_BASE_URL from config
      const { API_BASE_URL } = await import('../../config');

      // Determine if we need to use the conversion endpoint (legacy) or new LLM endpoint
      let endpoint = `${API_BASE_URL}/llm`;

      // Handle HTTPS fallback for production servers
      if (window.location.protocol === 'https:' &&
          (window.location.hostname === 'steelnet.ai' ||
           window.location.hostname === '************')) {
        // Try to use the proxy first
        endpoint = `/api/llm`;
        console.log('Using proxy URL for HTTPS fallback:', endpoint);
      }

      console.log('Sending message to API', {
        endpoint,
        text: userMessage,
        unit_system: unitSystem,
        function: effectiveActiveFunctions.join(',')
      });

      // Create a placeholder assistant message for streaming
      const placeholderMessage: ChatMessage = {
        type: 'assistant' as const,
        content: '',
        function: effectiveActiveFunctions[0],
        timestamp: new Date()
      };

      // Add placeholder message and capture its index
      let messageIndex: number;
      setMessages(prev => {
        const updatedMessages = [...prev, placeholderMessage];
        messageIndex = updatedMessages.length - 1;
        setStreamingMessageIndex(messageIndex);
        return updatedMessages;
      });

      // Start streaming
      setIsStreaming(true);
      setStreamingContent('');

      // Reset accumulated content for new stream
      accumulatedStreamingContentRef.current = '';

      // Reset progressive table parsing
      setStreamingTableData(null);
      streamingTableParserRef.current = new StreamingTableParser();

      // Start the streaming request
      await streamingService.startStream({
        text: userMessage,
        unit_system: unitSystem,
        function: effectiveActiveFunctions.join(','),
        sessionId: sessionId,
        onChunk: (chunk: StreamingChunk) => {
          if (chunk.type === 'content') {
            // Accumulate content immediately
            accumulatedStreamingContentRef.current += chunk.content || '';

            // Progressive table parsing
            if (streamingTableParserRef.current) {
              const currentTableData = streamingTableParserRef.current.addChunk(chunk.content || '');
              console.log('Progressive table parsing:', {
                chunkContent: chunk.content,
                isValid: currentTableData.isValid,
                headers: currentTableData.headers,
                rowCount: currentTableData.rows.length,
                firstRowData: currentTableData.rows[0]
              });
              if (currentTableData.isValid && currentTableData.headers.length > 0) {
                console.log('Setting streaming table data:', {
                  headers: currentTableData.headers,
                  rowCount: currentTableData.rows.length,
                  sampleRow: currentTableData.rows[0]
                });
                setStreamingTableData(currentTableData);
              } else {
                console.log('Table data not valid yet:', {
                  isValid: currentTableData.isValid,
                  headerCount: currentTableData.headers.length,
                  error: currentTableData.error
                });
              }
            }

            // Clear existing timeout
            if (streamingUpdateTimeoutRef.current) {
              clearTimeout(streamingUpdateTimeoutRef.current);
            }

            // Debounce UI updates to reduce DOM manipulation frequency
            streamingUpdateTimeoutRef.current = setTimeout(() => {
              const newContent = accumulatedStreamingContentRef.current;
              setStreamingContent(newContent);

              // Lightweight update to show streaming progress
              setMessages(prevMessages => {
                const newMessages = [...prevMessages];
                if (messageIndex < newMessages.length && newMessages[messageIndex]) {
                  newMessages[messageIndex] = {
                    ...newMessages[messageIndex],
                    content: newContent
                  };
                }
                return newMessages;
              });
            }, 50); // Update UI every 50ms instead of every chunk
          }
          // Don't process completion chunks during streaming - wait for onComplete
        },
        onComplete: async (result: any) => {
          console.log('Stream completed', result);
          
          // Clear any pending timeouts
          if (streamingUpdateTimeoutRef.current) {
            clearTimeout(streamingUpdateTimeoutRef.current);
            streamingUpdateTimeoutRef.current = null;
          }
          
          setIsStreaming(false);
          setStreamingContent('');
          setStreamingMessageIndex(null);

          // Reset streaming table data when streaming completes - CRITICAL FIX
          console.log('Streaming completed, resetting table data');
          setStreamingTableData(null);
          streamingTableParserRef.current = null;
          
          // Also reset accumulated streaming content to prevent interference
          accumulatedStreamingContentRef.current = '';

          // Small delay to ensure UI updates properly
          setTimeout(() => {
            // Now do the heavy processing after streaming is complete
            setMessages(prev => {
              const newMessages = [...prev];
              if (messageIndex < newMessages.length && newMessages[messageIndex]) {
                // Use the accumulated content as the final content - THIS IS THE KEY FIX
                const finalContent = accumulatedStreamingContentRef.current || newMessages[messageIndex].content;
              
              // Check if the response contains a table after stream completion
              const hasTable = (result && result.converted_text && 
                              (result.hasTable || containsMarkdownTable(result.converted_text))) ||
                              containsMarkdownTable(finalContent);

              // IMPORTANT: Keep the streamed content, don't overwrite with result.message
              newMessages[messageIndex] = {
                ...newMessages[messageIndex],
                content: finalContent, // Use the actual streamed content, not result.message
                result: result ? {
                  ...result,
                  hasTable: hasTable || effectiveActiveFunctions.includes('table'),
                  converted_text: result.converted_text || finalContent // Ensure converted_text is available
                } : undefined
              };
            }
            return newMessages;
          });
          
          // Reset accumulated content
          accumulatedStreamingContentRef.current = '';

          // Save the final message to the session
          try {
            setMessages(prev => {
              const finalMessage = prev[messageIndex];
              if (finalMessage) {
                saveMessage(sessionId, finalMessage).then(() => {
                  console.log(`Successfully saved assistant message to session ${sessionId}`);
                  
                  // Trigger a chat update event to refresh the sidebar
                  window.dispatchEvent(new CustomEvent('chat-updated'));
                }).catch(saveError => {
                  console.warn(`Failed to save assistant message to session ${sessionId}, but chat flow continues`, saveError);
                  setError('助手回复未保存，但对话已完成。');
                  setTimeout(() => setError(''), 3000);
                });
              }
              return prev;
            });
          } catch (saveError) {
            console.warn(`Failed to save assistant message to session ${sessionId}, but chat flow continues`, saveError);
            setError('助手回复未保存，但对话已完成。');
            setTimeout(() => setError(''), 3000);
          }
          }, 100); // Close the setTimeout for streaming completion
        },
        onError: (error: string) => {
          console.error('Streaming error:', error);
          
          // Clear any pending timeouts
          if (streamingUpdateTimeoutRef.current) {
            clearTimeout(streamingUpdateTimeoutRef.current);
            streamingUpdateTimeoutRef.current = null;
          }
          
          setIsStreaming(false);
          setStreamingContent('');
          
          // Reset accumulated content
          accumulatedStreamingContentRef.current = '';
          
          // Update the message with error content
          setError(`抱歉，处理过程中发生错误: ${error}。请稍后再试或联系支持团队。`);

          const errorMessage: ChatMessage = {
            type: 'assistant' as const,
            content: `处理失败: ${error}。请检查您的输入内容或网络连接。`,
            timestamp: new Date()
          };

          // Update the placeholder message with error
          setMessages(prev => {
            const newMessages = [...prev];
            if (messageIndex < newMessages.length && newMessages[messageIndex]) {
              newMessages[messageIndex] = errorMessage;
            }
            return newMessages;
          });

          // Save the error message
          saveMessage(sessionId, errorMessage).catch(console.error);
          setStreamingMessageIndex(null);
        }
      });

    } catch (error) {
      console.error('Processing error:', error);
      setIsStreaming(false);
      setStreamingContent('');
      setStreamingMessageIndex(null);

      // Get more detailed error message if available
      const errorMsg = error instanceof Error ? error.message : '未知错误';
      setError(`抱歉，处理过程中发生错误: ${errorMsg}。请稍后再试或联系支持团队。`);

      const errorMessage: ChatMessage = {
        type: 'assistant' as const,
        content: `处理失败: ${errorMsg}。请检查您的输入内容或网络连接。`,
        timestamp: new Date()
      };

      // Always show the error message in the UI for better user experience
      console.log('Adding error message to UI:', errorMessage);
      setMessages(prev => {
        const updatedMessages = [...prev, errorMessage];
        console.log('Updated messages state with error message:', updatedMessages);
        return updatedMessages;
      });

      // Save the error to the original session
      await saveMessage(sessionId, errorMessage);
    } finally {
      setIsLoading(false);

      // Only focus back on input if we're still on the same session
      try {
        const currentActiveSessionId = await getActiveSession();
        if (sessionId === currentActiveSessionId) {
          setTimeout(() => inputRef.current?.focus(), 100);
        }
      } catch (error) {
        console.error('Failed to get active session:', error);
        // Focus back on input anyway
        setTimeout(() => inputRef.current?.focus(), 100);
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;

    const userMessage = input.trim();
    setInput('');
    setError(null);

    // Determine the active function - if none selected, use 'general'
    const effectiveActiveFunctions = activeFunctions.length === 0 ? ['general'] : activeFunctions;

    const newUserMessage: ChatMessage = {
      type: 'user',
      content: userMessage,
      timestamp: new Date(),
      function: effectiveActiveFunctions[0]
    };

    // Capture the session ID at the time of submission to ensure the response
    // is added to the correct session, even if the user switches later
    let submissionSessionId = activeSessionId;

    // If no active session, create one immediately without blocking the user
    if (!submissionSessionId) {
      console.log('No active session found. Creating a new session on-the-fly.');

      // Create a temporary session ID for immediate use
      const tempSessionId = uuidv4();

      // Create a temporary session in memory
      const tempSession: ChatSession = {
        id: tempSessionId,
        title: userMessage.substring(0, 20) + '...',
        lastUpdated: new Date(),
        messages: [{
          type: 'assistant',
          content: t('welcomeMessageContent'),
          timestamp: new Date()
        }, newUserMessage],
        function: effectiveActiveFunctions[0],
        preview: userMessage.substring(0, 50)
      };

      // Update UI immediately
      setActiveSessionId(tempSessionId);
      setCurrentSession(tempSession);
      setMessages(tempSession.messages);
      setIsInInitialState(false);

      // Add to cache
      sessionCache[tempSessionId] = tempSession;

      // Start creating the real session in the background
      createSession(newUserMessage)
        .then(newSession => {
          console.log('Created permanent session with ID:', newSession.id);
          // Update with the real session ID once it's created
          setActiveSessionId(newSession.id);
          setCurrentSession(newSession);
          // No need to update messages as they should be the same
        })
        .catch(error => {
          console.error('Failed to create a permanent session:', error);
          // Continue with the temporary session - don't block the user
        });

      // Continue with the temporary session ID
      submissionSessionId = tempSessionId;
    }

    // Add message to UI right away
    console.log('Adding user message to UI:', newUserMessage);
    setMessages(prev => {
      const updatedMessages = [...prev, newUserMessage];
      console.log('Updated messages state:', updatedMessages);
      return updatedMessages;
    });

    // If this is the first user message, the chat is no longer in initial state
    if (isInInitialState) {
      setIsInInitialState(false);
      console.log('Chat transitioned from initial state to history');

      // Trigger a chat update event to refresh the sidebar
      // This ensures the chat appears in the sidebar as soon as it's no longer in initial state
      window.dispatchEvent(new CustomEvent('chat-updated'));
    }

    // Try to save message to session storage, but continue flow even if it fails
    try {
      console.log('=== CONVERSION CHAT: SAVE MESSAGE START ===');
      console.log(`Saving user message to session ${submissionSessionId}`);
      console.log('Message type:', newUserMessage.type);
      console.log('Message content:', newUserMessage.content);

      // Explicitly trigger a chat update event BEFORE saving
      // This ensures the sidebar is refreshed even if saving fails
      console.log('Dispatching chat-updated event BEFORE saving message');
      window.dispatchEvent(new CustomEvent('chat-updated'));

      const updatedSession = await saveMessage(submissionSessionId, newUserMessage);

      if (!updatedSession) {
        console.warn(`Failed to save message to session ${submissionSessionId}, but continuing chat flow`);
        // Show a non-blocking warning instead of an error that stops the flow
        setError('消息未保存，但对话将继续。');
      } else {
        console.log(`Successfully saved message to session ${submissionSessionId}`);

        // Explicitly trigger a chat update event AFTER saving
        // This ensures the chat appears in the sidebar immediately
        console.log('Dispatching chat-updated event AFTER saving message');
        window.dispatchEvent(new CustomEvent('chat-updated'));
      }

      console.log('=== CONVERSION CHAT: SAVE MESSAGE END ===');
    } catch (error) {
      console.warn('Error saving message, but continuing chat flow:', error);
      // Show a non-blocking warning
      setError('消息保存失败，但对话将继续。');

      // Even if saving fails, still try to update the sidebar
      console.log('Dispatching chat-updated event despite save error');
      window.dispatchEvent(new CustomEvent('chat-updated'));
    }

    // Clear error after 3 seconds
    setTimeout(() => setError(''), 3000);

    setIsLoading(true);

    // Process the user message and get AI response
    await processUserMessage(userMessage, submissionSessionId, effectiveActiveFunctions, newUserMessage);
  };

  const calculateWeight = (value: number, unit: string): number => {
    // Simple weight calculation for example purposes
    // In a real app, this would use proper steel density calculations
    return value * 7.85 * 0.001; // Simple approximation for steel in kg
  };

  const handleCopy = (index: number, text: string, format: 'text' | 'html' = 'text') => {
    if (format === 'html') {
      // Create a temporary element to hold the HTML content
      const tempElement = document.createElement('div');
      tempElement.innerHTML = text;

      // Select the element
      document.body.appendChild(tempElement);
      const range = document.createRange();
      range.selectNode(tempElement);
      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        selection.addRange(range);
        document.execCommand('copy');
        selection.removeAllRanges();
      }
      document.body.removeChild(tempElement);
    } else {
      // Regular text copy
      navigator.clipboard.writeText(text);
    }

    setCopied(index);
    setTimeout(() => setCopied(null), 2000);
  };

  // Copy table in Excel-friendly format
  const handleCopyTableForExcel = (index: number, markdownTable: string) => {
    try {
      // Parse the table and generate enhanced HTML format
      const parsedTable = parseMarkdownTable(markdownTable);
      if (!parsedTable.isValid) {
        throw new Error('Invalid table format');
      }

      const htmlTable = tableToExcelHTML(parsedTable);
      const tsvData = tableToTSV(parsedTable);

      // Try to copy as HTML with TSV fallback
      try {
        const clipboardItem = new ClipboardItem({
          'text/html': new Blob([htmlTable], { type: 'text/html' }),
          'text/plain': new Blob([tsvData], { type: 'text/plain' })
        });
        navigator.clipboard.write([clipboardItem]).then(() => {
          setCopied(index);
          setTimeout(() => setCopied(null), 2000);
        });
      } catch (clipboardError) {
        // Fallback to regular copy
        handleCopy(index, tsvData);
      }
    } catch (error) {
      console.error('Error copying table for Excel:', error);
      // Fallback to regular text copy
      handleCopy(index, markdownTable);
    }
  };

  const handleLike = (index: number) => {
    if (liked.includes(index)) {
      setLiked(liked.filter(i => i !== index));
    } else {
      setLiked([...liked, index]);
    }
  };

  const handleFeedback = (index: number) => {
    // Implement feedback functionality
    alert(t('feedbackThanks'));
  };

  const handleReportError = (index: number) => {
    // Implement error reporting
    alert(t('errorReportThanks'));
  };

  const handleConversionModeChange = (_: React.MouseEvent<HTMLElement>, newMode: 'imperial-to-metric' | 'metric-to-imperial' | null) => {
    if (newMode !== null) {
      setConversionMode(newMode);
    }
  };

  const handleEmailResult = (result: ConversionResult | TableData, type: 'conversion' | 'table' = 'conversion') => {
    // Set the selected result data and open the email dialog
    setSelectedResultData(result);
    setEmailResultType(type);
    setEmailDialogOpen(true);
  };

  // Handle Excel download for tables
  const handleExcelDownload = (data: any, type: 'markdown' | 'json' = 'markdown') => {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `conversion_table_${timestamp}.xlsx`;

      if (type === 'markdown') {
        // For markdown tables (from message.result.converted_text)
        convertMarkdownTableToExcel(data, fileName);
      } else {
        // For JSON data (from message.tableData)
        convertJsonTableToExcel(data, fileName);
      }
    } catch (error) {
      console.error('Error downloading Excel file:', error);
      setError('Excel文件下载失败，请稍后再试。');
      setTimeout(() => setError(null), 3000);
    }
  };

  // Format timestamp
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Get the active function name
  const getActiveFunctionName = () => {
    // If no functions are selected or only general is selected, return the general function name
    if (activeFunctions.length === 0 || (activeFunctions.length === 1 && activeFunctions[0] === 'general')) {
      const generalFunc = functions.find(f => f.id === 'general');
      return generalFunc ? generalFunc.name : '钢铁助手';
    }

    // Otherwise return the name of the first selected function
    const activeFunc = functions.find(f => activeFunctions.includes(f.id));
    return activeFunc ? activeFunc.name : '钢铁助手';
  };

  // Get the active function icon
  const getActiveFunctionIcon = () => {
    // If no functions are selected or only general is selected, return the general function icon
    if (activeFunctions.length === 0 || (activeFunctions.length === 1 && activeFunctions[0] === 'general')) {
      const generalFunc = functions.find(f => f.id === 'general');
      return generalFunc ? getIconForFunction(generalFunc.icon) : <SmartToyIcon />;
    }

    // Otherwise return the icon of the first selected function
    const activeFunc = functions.find(f => activeFunctions.includes(f.id));
    return activeFunc ? getIconForFunction(activeFunc.icon) : <SmartToyIcon />;
  };

  const toggleFunction = (id: string) => {
    // Don't allow toggling "general" function as it's the default
    if (id === 'general') return;

    if (activeFunctions.includes(id)) {
      // Remove the function if it's active
      setActiveFunctions(activeFunctions.filter(f => f !== id));
    } else {
      // Add the function while keeping others
      const newActiveFunctions = [...activeFunctions.filter(f => f !== 'general'), id];
      setActiveFunctions(newActiveFunctions);
    }
  };

  // Monitor active session changes
  useEffect(() => {
    const checkActiveSession = async () => {
      try {
        const currentActiveSessionId = await getActiveSession();

        // Only reload if the active session has changed
        if (currentActiveSessionId && currentActiveSessionId !== activeSessionId) {
          setIsSessionSwitching(true);
          try {
            // Always fetch fresh data from storage/API
            console.log("Fetching fresh session data from storage:", currentActiveSessionId);
            const freshSession = await getSession(currentActiveSessionId);

            if (freshSession) {
              // Update with the fresh data
              setCurrentSession(freshSession);
              setMessages(freshSession.messages);
              setActiveSessionId(currentActiveSessionId);
            }
            setIsSessionSwitching(false);
          } catch (error) {
            console.error('Failed to load active session:', error);
            setIsSessionSwitching(false);
          }
        }
      } catch (error) {
        console.error('Failed to get active session:', error);
      }
    };

    // Check active session changes more frequently to ensure we quickly
    // detect changes and update the UI accordingly
    const intervalId = setInterval(checkActiveSession, 300);

    // Initial check
    checkActiveSession();

    return () => clearInterval(intervalId);
  }, [activeSessionId]);

  // Cleanup streaming when session changes or component unmounts
  useEffect(() => {
    return () => {
      // Abort any active streams when session changes or component unmounts
      if (activeSessionId) {
        streamingService.abortStream(activeSessionId);
      }
      // Reset streaming state
      setIsStreaming(false);
      setStreamingContent('');
      setStreamingMessageIndex(null);
    };
  }, [activeSessionId]);

  // Cleanup all streams on component unmount
  useEffect(() => {
    return () => {
      streamingService.abortAllStreams();
    };
  }, []);

  // Clear debouncing timeout on unmount
  useEffect(() => {
    return () => {
      if (streamingUpdateTimeoutRef.current) {
        clearTimeout(streamingUpdateTimeoutRef.current);
      }
    };
  }, []);

  return (
    <Box sx={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      position: 'relative',
      backgroundColor: theme.palette.background.default, // Use dark theme background
    }}>
      {/* Error alert */}
      {error && (
        <Fade in={!!error}>
          <Alert
            severity="error"
            sx={{ m: 2 }}
            onClose={() => setError(null)}
          >
            {error}
          </Alert>
        </Fade>
      )}

      {/* Chat messages */}
      <Box
        sx={{
          flexGrow: 1,
          overflowY: 'auto',
          p: { xs: 2, md: 3 },
          display: 'flex',
          flexDirection: 'column',
          gap: 3,
          justifyContent: messages.length === 0 ? 'center' : 'flex-start',
          alignItems: messages.length === 0 ? 'center' : 'stretch',
        }}
      >
        {/* Don't render duplicate welcome messages */}
        {messages.map((message, index) => {
          // If this is a welcome message and not the first one, don't show it
          const isWelcome = isWelcomeMessage(message);
          const isFirst = index === 0;
          const showMessage = !isWelcome || isFirst || message.type === 'user';

          if (!showMessage) return null;

          return (
            <Fade key={index} in={true} timeout={300} style={{ transitionDelay: `${50 * index}ms` }}>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: message.type === 'user' ? 'row-reverse' : 'row',
                  alignItems: 'flex-start',
                  maxWidth: '1200px',
                  mx: 'auto',
                  width: '100%',
                }}
              >
                {/* Avatar for assistant */}
                {message.type === 'assistant' && (
                  <Avatar 
                    sx={{ 
                      bgcolor: '#404040', // Dark neutral color instead of primary green
                      alignSelf: 'flex-start',
                      mr: 2,
                      width: 30, 
                      height: 30,
                    }}
                  >
                    <SmartToyIcon sx={{ fontSize: 18 }} />
                  </Avatar>
                )}

                {/* Avatar for user */}
                {message.type === 'user' && (
                <Avatar
                  sx={{
                      bgcolor: '#404040', // Dark neutral color instead of primary green
                      alignSelf: 'flex-start',
                      ml: 2,
                      width: 30,
                      height: 30,
                    }}
                  >
                    <PersonIcon sx={{ fontSize: 18 }} />
                </Avatar>
                )}

                {/* Message content */}
                <Box
                  sx={{
                    flexGrow: 0,
                    maxWidth: message.type === 'user' ? '70%' : '90%', // Wider for assistant messages with diagrams
                    backgroundColor: message.type === 'user' ? '#404040' : '#2a2a2a', // Changed from theme.palette.primary.main to dark neutral
                    color: message.type === 'user' ? '#ececec' : '#ececec', // Light text for both
                    p: 2,
                    borderRadius: message.type === 'user'
                      ? '1rem 0 1rem 1rem' // User message bubble shape
                      : '0 1rem 1rem 1rem', // Assistant message bubble shape
                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
                    position: 'relative',
                  }}
                >
                  {/* Show function type for user messages */}
                  {message.type === 'user' && message.function && (
                    <Chip
                      size="small"
                      label={functions.find(f => f.id === message.function)?.name || message.function}
                      icon={getIconForFunction(functions.find(f => f.id === message.function)?.icon || 'chat')}
                      sx={{
                        mb: 1,
                        bgcolor: 'rgba(42, 42, 42, 0.9)',
                        color: '#ececec',
                        fontWeight: 'medium',
                        '& .MuiChip-icon': { color: '#ececec' }
                      }}
                    />
                  )}

                  {(() => {
                    // Parse message content for converted content tags
                    const { hasTable, tableContent, cleanContent, tableData: messageTableData } = parseConvertedContent(message.content);

                    // Debug logging
                    console.log('Rendering message:', {
                      messageIndex: index,
                      isLastMessage: index === messages.length - 1,
                      isStreamingMessage: streamingMessageIndex === index,
                      hasTable,
                      messageTableData,
                      globalStreamingTableData: streamingTableData,
                      streamingTableDataValid: streamingTableData?.isValid,
                      messageContent: message.content.substring(0, 100) + '...'
                    });

                    return (
                      <>
                        {/* Main content (text without converted_content tags) - only show if not streaming */}
                        {cleanContent && streamingMessageIndex !== index && (
                          <Typography
                            variant="body1"
                            sx={{
                              whiteSpace: 'pre-wrap',
                              color: message.type === 'user' ? 'white' : 'text.primary',
                              fontWeight: message.type === 'user' ? 400 : 400,
                              lineHeight: 1.6,
                              wordBreak: 'break-word',
                              width: '100%',
                              '& pre': {
                                overflowX: 'auto',
                                backgroundColor: 'rgba(0, 0, 0, 0.05)',
                                padding: '0.75rem',
                                borderRadius: '0.5rem',
                                fontFamily: 'monospace',
                                fontSize: '0.9rem',
                                maxWidth: '100%'
                              },
                              '& code': {
                                fontFamily: 'monospace',
                                backgroundColor: 'rgba(0, 0, 0, 0.05)',
                                padding: '0.2rem 0.4rem',
                                borderRadius: '0.25rem',
                                fontSize: '0.9rem'
                              }
                            }}
                          >
                            {cleanContent}
                          </Typography>
                        )}

                        {(() => {
                          // Determine if this message is currently being streamed
                          const isCurrentlyStreaming = streamingMessageIndex === index && isStreaming;

                          // Show streaming table only if actively streaming and we have valid streaming table data
                          const shouldShowStreamingTable = isCurrentlyStreaming &&
                                                          streamingTableData &&
                                                          streamingTableData.isValid &&
                                                          streamingTableData.headers.length > 0;

                          // Show completed table only if NOT currently streaming AND we have table data AND no streaming table to show
                          const shouldShowCompletedTable = !isCurrentlyStreaming &&
                                                          !shouldShowStreamingTable &&
                                                          hasTable &&
                                                          (tableContent || messageTableData);

                          console.log('Table display logic:', {
                            messageIndex: index,
                            isCurrentlyStreaming,
                            shouldShowStreamingTable,
                            shouldShowCompletedTable,
                            hasStreamingData: !!streamingTableData,
                            streamingDataValid: streamingTableData?.isValid,
                            hasTable,
                            hasTableContent: !!tableContent,
                            hasMessageTableData: !!messageTableData
                          });

                          if (shouldShowStreamingTable) {
                            return (
                              <StreamingTableRenderer
                                streamingTableData={streamingTableData}
                                onCopy={(content) => handleCopy(index, content)}
                              />
                            );
                          }

                          if (shouldShowCompletedTable) {
                            return (
                              <RobustTableRenderer
                                content={tableContent}
                                tableData={messageTableData}
                                onCopy={(content) => handleCopy(index, content)}
                              />
                            );
                          }

                          return null;
                        })()}

                        {/* If no converted content, show raw content as fallback */}
                        {!hasTable && !cleanContent && (
                          <Typography
                            variant="body1"
                            sx={{
                              whiteSpace: 'pre-wrap',
                              color: message.type === 'user' ? 'white' : 'text.primary',
                              fontWeight: message.type === 'user' ? 400 : 400,
                              lineHeight: 1.6,
                              wordBreak: 'break-word',
                              width: '100%',
                              '& pre': {
                                overflowX: 'auto',
                                backgroundColor: 'rgba(0, 0, 0, 0.05)',
                                padding: '0.75rem',
                                borderRadius: '0.5rem',
                                fontFamily: 'monospace',
                                fontSize: '0.9rem',
                                maxWidth: '100%'
                              },
                              '& code': {
                                fontFamily: 'monospace',
                                backgroundColor: 'rgba(0, 0, 0, 0.05)',
                                padding: '0.2rem 0.4rem',
                                borderRadius: '0.25rem',
                                fontSize: '0.9rem'
                              }
                            }}
                          >
                            {message.content}
                          </Typography>
                        )}
                      </>
                    );
                  })()}

                  {/* Table View for legacy mode */}
                  {message.isTable && message.tableData && (
                    <Box sx={{ mt: 2 }}>
                      <Divider sx={{ my: 1 }} />
                      <TableContainer component={Paper} sx={{ 
                        boxShadow: 'none', 
                        border: '1px solid rgba(0,0,0,0.1)', 
                        mb: 2,
                        overflowX: 'auto', 
                        width: '100%',
                        backgroundColor: '#2a2a2a',
                        '& .MuiTableCell-root': {
                          backgroundColor: '#2a2a2a',
                          color: '#ececec',
                          borderColor: 'rgba(255,255,255,0.1)'
                        }
                      }}>
                        <Table size="small" sx={{ minWidth: 650, tableLayout: 'auto' }}>
                          <TableHead>
                            <TableRow>
                              <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>{t('steelType')}</TableCell>
                              <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>{t('length')} ({message.tableData?.length?.unit || 'mm'})</TableCell>
                              <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>{t('width')} ({message.tableData?.width?.unit || 'mm'})</TableCell>
                              <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>{t('thickness')} ({message.tableData?.thickness?.unit || 'mm'})</TableCell>
                              <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>{t('weight')} ({message.tableData?.weight?.unit})</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            <TableRow>
                              <TableCell sx={{ wordBreak: 'break-word' }}>{message.tableData.steelType}</TableCell>
                              <TableCell sx={{ wordBreak: 'break-word' }}>{message.tableData?.length?.value.toFixed(2) || '-'}</TableCell>
                              <TableCell sx={{ wordBreak: 'break-word' }}>{message.tableData?.width?.value.toFixed(2) || '-'}</TableCell>
                              <TableCell sx={{ wordBreak: 'break-word' }}>{message.tableData?.thickness?.value.toFixed(2) || '-'}</TableCell>
                              <TableCell sx={{ wordBreak: 'break-word' }}>{message.tableData?.weight?.value.toFixed(2) || '-'}</TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </TableContainer>

                      {/* Table result actions */}
                      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1, gap: 1 }}>
                        <Tooltip title={t('copyForExcel') || "Copy for Excel"}>
                          <IconButton
                            size="small"
                            onClick={() => {
                              // Convert tableData to markdown format first
                              if (!message.tableData) return;
                              
                              const headers = ['Steel Type', 'Length', 'Width', 'Thickness', 'Weight'];
                              const row = [
                                message.tableData.steelType || '',
                                `${message.tableData?.length?.value.toFixed(2) || '-'} ${message.tableData?.length?.unit || 'mm'}`,
                                `${message.tableData?.width?.value.toFixed(2) || '-'} ${message.tableData?.width?.unit || 'mm'}`,
                                `${message.tableData?.thickness?.value.toFixed(2) || '-'} ${message.tableData?.thickness?.unit || 'mm'}`,
                                `${message.tableData?.weight?.value.toFixed(2) || '-'} ${message.tableData?.weight?.unit || 'kg'}`
                              ];

                              // Create a simple HTML table
                              const htmlTable = `<table>
                                <tr>${headers.map(h => `<th>${h}</th>`).join('')}</tr>
                                <tr>${row.map(cell => `<td>${cell}</td>`).join('')}</tr>
                              </table>`;

                              handleCopy(index, htmlTable);
                            }}
                            color={copied === index ? 'success' : 'default'}
                          >
                            {copied === index ? <CheckIcon /> : <ContentCopyIcon />}
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('like')}>
                          <IconButton
                            size="small"
                            onClick={() => handleLike(index)}
                            color={liked.includes(index) ? 'primary' : 'default'}
                          >
                            {liked.includes(index) ? <ThumbUpAltIcon /> : <ThumbUpOutlinedIcon />}
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('reportError')}>
                          <IconButton
                            size="small"
                            onClick={() => handleReportError(index)}
                          >
                            <ErrorOutlineIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('feedback')}>
                          <IconButton
                            size="small"
                            onClick={() => handleFeedback(index)}
                          >
                            <FeedbackIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('downloadExcel')}>
                          <IconButton
                            size="small"
                            onClick={() => handleExcelDownload(message.tableData, 'json')}
                            color="primary"
                          >
                            <FileDownloadIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('sendToEmail')}>
                          <IconButton
                            size="small"
                            onClick={() => handleEmailResult(message.tableData!, 'table')}
                          >
                            <EmailIcon />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </Box>
                  )}

                  {/* Simple Conversion Result */}
                  {message.result && !message.isTable && (
                      <Box sx={{ mt: 2 }}>
                        <Divider sx={{ my: 1 }} />

                        {/* If the result has a table, render it as HTML */}
                        {message.result.hasTable ? (() => {
                          const { tableData: resultTableData } = parseConvertedContent(message.result.converted_text);
                          return (
                            <RobustTableRenderer
                              content={message.result.converted_text}
                              tableData={resultTableData}
                              onCopy={(content) => handleCopy(index, content)}
                            />
                          );
                        })() : (
                          // Regular text conversion display
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mt: 2 }}>
                            <Typography variant="subtitle2" sx={{
                              fontWeight: 'bold',
                              color: message.type === 'user' ? 'rgba(255,255,255,0.9)' : 'text.primary'
                            }}>
                              Original:
                            </Typography>
                            <Typography
                              variant="body2"
                              component="div"
                              sx={{
                                p: 1.5,
                                bgcolor: message.type === 'user' ? 'rgba(255,255,255,0.15)' : theme.palette.background.paper,
                                borderRadius: 1,
                                fontFamily: 'monospace',
                                whiteSpace: 'pre-wrap',
                                wordBreak: 'break-word',
                                color: message.type === 'user' ? 'rgba(255,255,255,0.9)' : 'text.primary'
                              }}
                            >
                              {message.result?.original_text}
                            </Typography>

                            <Typography variant="subtitle2" sx={{
                              fontWeight: 'bold',
                              mt: 1,
                              color: message.type === 'user' ? 'rgba(255,255,255,0.9)' : 'text.primary'
                            }}>
                              Converted:
                            </Typography>
                            <Typography
                              variant="body2"
                              component="div"
                              sx={{
                                p: 1.5,
                                bgcolor: message.type === 'user' ? 'rgba(255,255,255,0.25)' : theme.palette.background.paper,
                                borderRadius: 1,
                                fontFamily: 'monospace',
                                whiteSpace: 'pre-wrap',
                                wordBreak: 'break-word',
                                fontWeight: 'medium',
                                color: message.type === 'user' ? 'white' : 'text.primary'
                              }}
                            >
                              {message.result?.converted_text}
                            </Typography>
                          </Box>
                        )}

                      {/* Conversion result actions */}
                      <Box sx={{
                        display: 'flex',
                        justifyContent: 'flex-end',
                        mt: 1,
                        gap: 1,
                        '& .MuiIconButton-root': {
                          color: message.type === 'user' ? 'rgba(255,255,255,0.8)' : 'rgba(0,0,0,0.6)',
                          '&:hover': {
                            color: message.type === 'user' ? 'white' : 'rgba(0,0,0,0.8)',
                            backgroundColor: message.type === 'user' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'
                          }
                        }
                      }}>
                        {message.result?.hasTable ? (
                          <Tooltip title={t('copyForExcel') || "Copy for Excel"}>
                            <IconButton
                              size="small"
                              onClick={() => message.result && handleCopyTableForExcel(index, message.result.converted_text)}
                              color={copied === index ? 'success' : 'default'}
                            >
                              {copied === index ? <CheckIcon fontSize="small" /> : <ContentCopyIcon fontSize="small" />}
                            </IconButton>
                          </Tooltip>
                        ) : (
                          <Tooltip title={t('copy')}>
                            <IconButton
                              size="small"
                              onClick={() => handleCopy(index, message.result?.converted_text || '')}
                              color={copied === index ? 'success' : 'default'}
                            >
                              {copied === index ? <CheckIcon fontSize="small" /> : <ContentCopyIcon fontSize="small" />}
                            </IconButton>
                          </Tooltip>
                        )}
                        <Tooltip title={t('like')}>
                          <IconButton
                            size="small"
                            onClick={() => handleLike(index)}
                            color={liked.includes(index) ? 'primary' : 'default'}
                          >
                            {liked.includes(index) ? <ThumbUpAltIcon fontSize="small" /> : <ThumbUpOutlinedIcon fontSize="small" />}
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('reportError')}>
                          <IconButton
                            size="small"
                            onClick={() => handleReportError(index)}
                          >
                            <ErrorOutlineIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('feedback')}>
                          <IconButton
                            size="small"
                            onClick={() => handleFeedback(index)}
                          >
                            <FeedbackIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        {message.result?.hasTable && (
                          <Tooltip title={t('downloadExcel')}>
                            <IconButton
                              size="small"
                              onClick={() => message.result && handleExcelDownload(message.result.converted_text, 'markdown')}
                              color="primary"
                            >
                              <FileDownloadIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                        <Tooltip title={t('sendToEmail')}>
                          <IconButton
                            size="small"
                            onClick={() => message.result && handleEmailResult(message.result, 'conversion')}
                          >
                            <EmailIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                      </Box>
                    )}

                  {/* Message timestamp */}
                  <Box sx={{
                    display: 'flex',
                    justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',
                    alignItems: 'center',
                    mt: 1,
                    opacity: 0.7
                  }}>
                    <Typography variant="caption" sx={{ fontSize: '0.7rem', color: message.type === 'user' ? 'text.secondary' : 'text.secondary' }}>
                      {formatTime(message.timestamp)}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Fade>
          );
        })}
        <div ref={messagesEndRef} />
      </Box>

      {/* Loading indicator */}
      {isLoading && (
        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          p: 2,
          position: 'relative',
          gap: 1
        }}>
          <CircularProgress size={30} thickness={4} sx={{ color: 'primary.main' }} />
          <Typography variant="body2" color="text.secondary">
            {t('working')}
          </Typography>
        </Box>
      )}

      {/* Input form - ChatGPT style */}
      <Box
        sx={{
          p: 0,
          position: 'relative',
          width: '100%',
          ...(messages.length === 0 && {
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 1,
          })
        }}
      >
        <Box
          component="form"
          onSubmit={handleSubmit}
          sx={{
            p: 2,
            maxWidth: '800px',
            mx: 'auto',
            width: '100%',
          }}
        >


          <Paper
            elevation={0}
            sx={{
              display: 'flex',
              flexDirection: 'column',
              border: '1px solid',
              borderColor: 'divider',
              borderRadius: '0.75rem',
              '&:hover': {
                borderColor: 'primary.main',
              },
            }}
          >
            {/* Message input area */}
            <Box sx={{
              display: 'flex',
              p: 1.5,
              minHeight: '56px',
              alignItems: 'flex-end'
            }}>
              <TextField
                fullWidth
                multiline
                placeholder=""
                variant="standard"
                value={input}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setInput(e.target.value)}
                inputRef={inputRef}
                InputProps={{
                  disableUnderline: true,
                  sx: {
                    fontSize: '1rem',
                    lineHeight: 1.5,
                  }
                }}
                maxRows={8}
                sx={{ flexGrow: 1 }}
              />

              <IconButton
                color="primary"
                type="submit"
                disabled={isLoading || !input.trim()}
                sx={{
                  bgcolor: input.trim() ? 'primary.main' : 'transparent',
                  color: input.trim() ? 'white' : 'text.disabled',
                  border: input.trim() ? 'none' : '1px solid',
                  borderColor: 'divider',
                  ml: 1,
                  '&:hover': {
                    bgcolor: input.trim() ? 'primary.dark' : 'rgba(0,0,0,0.04)',
                  },
                  '&.Mui-disabled': {
                    bgcolor: 'action.disabledBackground',
                    opacity: 0.5,
                  },
                  transition: 'all 0.3s',
                  width: 40,
                  height: 40,
                }}
              >
                <SendIcon />
              </IconButton>
            </Box>

            {/* Function buttons - bottom aligned like ChatGPT with more padding */}
            <Box sx={{
              borderTop: '1px solid',
              borderColor: 'divider',
              py: 1.5,
              px: 2.5,
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <Box sx={{ display: 'flex', gap: 1.5 }}>
                {!isLoadingFunctions ? (
                  functions
                    .filter(func => func.id !== 'general') // Don't show the 'general' function button
                    .map((func) => (
                      <Chip
                        key={func.id}
                        icon={getIconForFunction(func.icon)}
                        label={func.name}
                        variant={activeFunctions.includes(func.id) ? "filled" : "outlined"}
                        onClick={() => toggleFunction(func.id)}
                        color={activeFunctions.includes(func.id) ? "primary" : "default"}
                        size="small"
                        sx={{
                          borderRadius: '1rem',
                          height: '30px',
                          fontSize: '0.75rem',
                          px: 0.75,
                          cursor: 'pointer',
                          '&:hover': {
                            borderColor: 'primary.main',
                          }
                        }}
                      />
                    ))
                ) : (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <CircularProgress size={16} />
                    <Typography variant="caption">{t('loadingFunctions')}</Typography>
                  </Box>
                )}
              </Box>

              {activeFunctions.includes('conversion') && (
                <ToggleButtonGroup
                  value={conversionMode}
                  exclusive
                  onChange={handleConversionModeChange}
                  aria-label="conversion mode"
                  size="small"
                  sx={{ height: '30px', ml: 2 }}
                >
                  <ToggleButton value="imperial-to-metric" sx={{ px: 1.5, fontSize: '0.75rem' }}>
                    {t('imperialToMetric')}
                  </ToggleButton>
                  <ToggleButton value="metric-to-imperial" sx={{ px: 1.5, fontSize: '0.75rem' }}>
                    {t('metricToImperial')}
                  </ToggleButton>
                </ToggleButtonGroup>
              )}
            </Box>
          </Paper>


        </Box>
      </Box>

      {/* Email Dialog */}
      {emailDialogOpen && selectedResultData && (
        <EmailDialog
          open={emailDialogOpen}
          onClose={() => setEmailDialogOpen(false)}
          resultData={selectedResultData}
          resultType={emailResultType}
        />
      )}
    </Box>
  );
};

export default ConversionChat;

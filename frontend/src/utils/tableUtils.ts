/**
 * Utility functions for table operations
 */

/**
 * Parse a markdown table into headers and rows
 * @param markdownTable The markdown table content
 * @returns An object with headers and rows
 */
export interface ParsedTableRow {
  [key: string]: string;
}

export interface ParsedTable {
  headers: string[];
  rows: ParsedTableRow[];
  isValid: boolean;
  error?: string;
}

/**
 * Safely parse a markdown table with robust error handling
 * @param markdownTable The markdown table content
 * @returns An object with headers, rows, and validation info
 */
export const parseMarkdownTable = (markdownTable: string): ParsedTable => {
  try {
    if (!markdownTable || typeof markdownTable !== 'string') {
      return { headers: [], rows: [], isValid: false, error: 'Empty or invalid table content' };
    }

    const lines = markdownTable.trim().split('\n').filter(line => line.trim().length > 0);

    if (lines.length < 1) {
      return { headers: [], rows: [], isValid: false, error: 'No table content found' };
    }

    // Enhanced header parsing with better detection
    const headerLine = lines[0].trim();
    let headers: string[] = [];

    if (headerLine.includes('|')) {
      // Remove outer pipes if they exist and split
      const cleanHeader = headerLine.replace(/^\||\|$/g, '');
      headers = cleanHeader.split('|').map(h => h.trim());
      console.log('Parsed pipe-delimited headers:', {
        originalLine: headerLine,
        cleanedLine: cleanHeader,
        headers: headers,
        headerCount: headers.length
      });
    } else {
      // Try multiple separation methods for non-pipe tables
      if (headerLine.includes('\t')) {
        // Tab-separated - preserve empty cells to maintain column structure
        headers = headerLine.split('\t').map(h => h.trim());
        console.log('Parsed tab-delimited headers (preserving empty):', headers);
      } else if (headerLine.match(/\s{2,}/)) {
        // Multiple spaces - preserve structure
        headers = headerLine.split(/\s{2,}/).map(h => h.trim());
        console.log('Parsed space-delimited headers (preserving empty):', headers);
      } else {
        // Single space or comma separated as fallback
        headers = headerLine.split(/[,\s]+/).map(h => h.trim()).filter(h => h.length > 0);
      }
    }

    // Filter out completely empty headers but preserve the structure for multi-column tables
    if (headers.length === 0) {
      return { headers: [], rows: [], isValid: false, error: 'No valid headers found' };
    }

    // Ensure we have at least one non-empty header for a valid table
    const hasValidHeaders = headers.some(h => h.length > 0);
    if (!hasValidHeaders) {
      return { headers: [], rows: [], isValid: false, error: 'All headers are empty' };
    }

    // Find the separator line (might not be exactly line 1)
    let separatorIndex = -1;
    for (let i = 1; i < Math.min(3, lines.length); i++) {
      const line = lines[i].trim();
      if (line.match(/^[\|\s]*[-:]+[\|\s-:]*$/)) {
        separatorIndex = i;
        break;
      }
    }

    // If no separator found, try to parse without it (more flexible)
    let startRowIndex = separatorIndex !== -1 ? separatorIndex + 1 : 1;

    // Enhanced row parsing with better format detection
    const rows: ParsedTableRow[] = [];
    for (let i = startRowIndex; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line || line.startsWith('<!--') || line.match(/^[\|\s-:]*$/)) continue;

      let cells: string[] = [];

      if (line.includes('|')) {
        // Remove outer pipes if they exist and split
        const cleanLine = line.replace(/^\||\|$/g, '');
        cells = cleanLine.split('|').map(cell => cell.trim());
        console.log(`Parsed pipe-delimited cells for line ${i}:`, {
          originalLine: line,
          cleanedLine: cleanLine,
          cells: cells,
          cellCount: cells.length
        });
      } else {
        // Enhanced separation detection matching header parsing
        if (line.includes('\t')) {
          // Tab-separated - preserve empty cells to maintain column structure
          cells = line.split('\t').map(cell => cell.trim());
          console.log(`Parsed tab-delimited cells for line ${i} (preserving empty):`, cells);
        } else if (line.match(/\s{2,}/)) {
          // Multiple spaces - preserve structure
          cells = line.split(/\s{2,}/).map(cell => cell.trim());
          console.log(`Parsed space-delimited cells for line ${i} (preserving empty):`, cells);
        } else {
          // Single space or comma separated as fallback
          cells = line.split(/[,\s]+/).map(cell => cell.trim()).filter(cell => cell.length > 0);
        }
      }

      // Only add row if it has meaningful content and matches expected column count
      if (cells.some(cell => cell.length > 0)) {
        const row: ParsedTableRow = {};
        headers.forEach((header, index) => {
          row[header] = cells[index] || '';
        });
        rows.push(row);
      }
    }

    const isValid = headers.length > 0 && rows.length > 0;
    return { 
      headers, 
      rows, 
      isValid,
      error: isValid ? undefined : 'No valid table data found'
    };

  } catch (error) {
    console.error('Error parsing markdown table:', error);
    return { 
      headers: [], 
      rows: [], 
      isValid: false, 
      error: `Parsing error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
};

/**
 * Convert a parsed table to HTML format suitable for copying to Excel
 * @param table The parsed table object
 * @returns HTML string that can be copied to Excel
 */
export const tableToExcelHTML = (table: ParsedTable): string => {
  const { headers, rows, isValid } = table;

  if (!isValid || headers.length === 0 || rows.length === 0) {
    return '';
  }

  try {
    // Create HTML table with Excel-compatible formatting
    let html = '<table border="1" cellpadding="4" cellspacing="0" style="border-collapse: collapse; font-family: Arial, sans-serif;">';

    // Add header row with styling
    html += '<thead><tr style="background-color: #f0f0f0; font-weight: bold;">';
    headers.forEach(header => {
      const safeHeader = escapeHtml(header);
      html += `<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">${safeHeader}</th>`;
    });
    html += '</tr></thead>';

    // Add data rows with styling
    html += '<tbody>';
    rows.forEach((row, rowIndex) => {
      const rowStyle = rowIndex % 2 === 0 ? 'background-color: #ffffff;' : 'background-color: #f9f9f9;';
      html += `<tr style="${rowStyle}">`;
      headers.forEach(header => {
        const cellContent = row[header] || '';
        const safeCellContent = escapeHtml(cellContent);
        // Detect if content is numeric for right alignment
        const isNumeric = /^[\d,.-]+$/.test(cellContent.trim());
        const textAlign = isNumeric ? 'right' : 'left';
        html += `<td style="border: 1px solid #ccc; padding: 8px; text-align: ${textAlign};">${safeCellContent}</td>`;
      });
      html += '</tr>';
    });
    html += '</tbody>';

    html += '</table>';

    return html;
  } catch (error) {
    console.error('Error converting table to HTML:', error);
    return '';
  }
};

/**
 * Convert a parsed table to tab-separated values format
 * @param table The parsed table object
 * @returns TSV string that can be copied to Excel or other spreadsheet applications
 */
export const tableToTSV = (table: ParsedTable): string => {
  const { headers, rows, isValid } = table;

  if (!isValid || headers.length === 0 || rows.length === 0) {
    return '';
  }

  try {
    // Create TSV format
    let tsv = headers.join('\t') + '\n';

    rows.forEach(row => {
      const rowData = headers.map(header => {
        const cellContent = row[header] || '';
        // Escape tabs and newlines in cell content
        return cellContent.replace(/\t/g, ' ').replace(/\n/g, ' ').replace(/\r/g, '');
      });
      tsv += rowData.join('\t') + '\n';
    });

    return tsv;
  } catch (error) {
    console.error('Error converting table to TSV:', error);
    return '';
  }
};

/**
 * Escape HTML characters to prevent XSS
 * @param text The text to escape
 * @returns Escaped HTML string
 */
const escapeHtml = (text: string): string => {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
};

/**
 * Convert a markdown table to HTML format suitable for copying to Excel
 * @param markdownTable The markdown table content
 * @returns HTML string that can be copied to Excel
 */
export const markdownTableToExcelHTML = (markdownTable: string): string => {
  try {
    const parsedTable = parseMarkdownTable(markdownTable);
    return tableToExcelHTML(parsedTable);
  } catch (error) {
    console.error('Error converting markdown table to Excel HTML:', error);
    return '';
  }
};

// Simple cache for table detection to avoid re-parsing the same content
const tableDetectionCache = new Map<string, boolean>();
const CACHE_MAX_SIZE = 100;

/**
 * Enhanced table detection with multiple strategies and performance optimizations
 * @param text The text to check
 * @returns True if the text contains a markdown table
 */
export const containsMarkdownTable = (text: string): boolean => {
  if (!text || typeof text !== 'string') {
    return false;
  }

  // Quick early exit for obviously non-table content
  if (text.length < 10 || (!text.includes('|') && !text.includes('\t') && !text.match(/\s{3,}/))) {
    return false;
  }

  // Check cache first
  const cacheKey = text.slice(0, 200); // Use first 200 chars as cache key
  if (tableDetectionCache.has(cacheKey)) {
    return tableDetectionCache.get(cacheKey)!;
  }

  try {
    const lines = text.trim().split('\n').filter(line => line.trim().length > 0);
    if (lines.length < 2) {
      cacheResult(cacheKey, false);
      return false;
    }

    // Strategy 1: Quick check for classic markdown table (with separators)
    const hasTableMarkers = text.includes('|');
    if (hasTableMarkers) {
      const hasSeparatorLine = lines.some(line => line.match(/^[\|\s]*[-:]+[\|\s-:]*$/));
      if (hasSeparatorLine) {
        cacheResult(cacheKey, true);
        return true;
      }

      // Strategy 2: Look for multiple lines with consistent pipe structure
      const pipeLines = lines.filter(line => line.includes('|'));
      if (pipeLines.length >= 2) {
        // Quick check - if first two lines have similar pipe counts, likely a table
        const firstPipes = (pipeLines[0].match(/\|/g) || []).length;
        const secondPipes = (pipeLines[1].match(/\|/g) || []).length;
        
        if (firstPipes >= 2 && Math.abs(firstPipes - secondPipes) <= 1) {
          cacheResult(cacheKey, true);
          return true;
        }
      }
    }

    // Strategy 3: Tab-separated or space-separated tables (quick check only)
    if (text.includes('\t')) {
      const tabLines = lines.filter(line => line.includes('\t'));
      if (tabLines.length >= 2) {
        cacheResult(cacheKey, true);
        return true;
      }
    }

    cacheResult(cacheKey, false);
    return false;
  } catch (error) {
    console.error('Error checking for markdown table:', error);
    cacheResult(cacheKey, false);
    return false;
  }
};

/**
 * Helper function to cache results and manage cache size
 */
const cacheResult = (key: string, result: boolean): void => {
  // Clear cache if it gets too large
  if (tableDetectionCache.size >= CACHE_MAX_SIZE) {
    tableDetectionCache.clear();
  }
  tableDetectionCache.set(key, result);
};

/**
 * Progressive streaming table parser for real-time table building
 * Enhanced with fixed header format and robust buffering
 */
export class StreamingTableParser {
  private headers: string[] = [];
  private rows: ParsedTableRow[] = [];
  private isHeaderParsed = false;
  private buffer = '';
  private headerBuffering = false;
  private expectedHeaders = ['Item Code', 'Description', 'Size (Original)', 'Customer QTY', 'Size (Converted)', 'Converted QTY', 'Remarks'];

  /**
   * Add streaming content and parse incrementally
   * @param chunk New content chunk
   * @returns Current table state
   */
  addChunk(chunk: string): ParsedTable {
    this.buffer += chunk;
    console.log('StreamingTableParser.addChunk:', {
      chunkLength: chunk.length,
      bufferLength: this.buffer.length,
      chunkContent: chunk,
      hasTableStreamStart: this.buffer.includes('<table_stream>'),
      hasTableStreamEnd: this.buffer.includes('</table_stream>')
    });

    // Look for table_stream tags - enhanced matching
    const streamMatch = this.buffer.match(/<table_stream>([\s\S]*?)(?:<\/table_stream>|$)/);
    if (streamMatch) {
      const tableContent = streamMatch[1];
      console.log('Found table content in buffer:', {
        tableContentLength: tableContent.length,
        tableContent: tableContent,
        hasNewlines: tableContent.includes('\n'),
        lineCount: tableContent.split('\n').length
      });
      return this.parseStreamingContent(tableContent);
    }

    console.log('No table_stream match yet, returning invalid table');
    return { headers: this.headers, rows: this.rows, isValid: false, error: 'Waiting for table_stream content' };
  }

  /**
   * Parse streaming table content using robust delimiter detection
   * @param content Table content within stream tags
   * @returns Current parsed table state
   */
  private parseStreamingContent(content: string): ParsedTable {
    console.log('🔄 parseStreamingContent called:', {
      contentLength: content.length,
      content: content,
      hasNewlines: content.includes('\n')
    });

    const lines = content.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    console.log('📋 Split into lines:', {
      totalLines: lines.length,
      lines: lines
    });

    // Parse headers using robust delimiter detection
    if (!this.isHeaderParsed && lines.length > 0) {
      const headerLine = lines[0];
      let headers: string[] = [];
      let delimiter = '|'; // Default delimiter

      console.log('🔍 Parsing headers from first line:', headerLine);

      // Robust delimiter detection and header parsing
      if (headerLine.includes('|')) {
        // Remove outer pipes if they exist and split
        const cleanHeader = headerLine.replace(/^\||\|$/g, '');
        headers = cleanHeader.split('|').map(h => h.trim());
        delimiter = '|';
        console.log('✅ Pipe-delimited headers detected:', { headers, delimiter });
      } else if (headerLine.includes('\t')) {
        headers = headerLine.split('\t').map(h => h.trim());
        delimiter = '\t';
        console.log('✅ Tab-delimited headers detected:', { headers, delimiter });
      } else if (headerLine.match(/\s{2,}/)) {
        headers = headerLine.split(/\s{2,}/).map(h => h.trim());
        delimiter = 'space';
        console.log('✅ Space-delimited headers detected:', { headers, delimiter });
      } else {
        headers = [headerLine.trim()];
        delimiter = 'single';
        console.log('✅ Single header detected:', { headers, delimiter });
      }

      // Validate header completeness
      if (headers.length >= 6) {
        console.log('🎯 Header line appears complete, setting headers');
        this.headers = headers;
        this.isHeaderParsed = true;

        console.log('🔍 Final header validation:', {
          headerCount: headers.length,
          headers: headers,
          delimiter: delimiter
        });
      } else {
        console.log('⏳ Header line appears incomplete, waiting for more content...');
        return {
          headers: this.expectedHeaders,
          rows: [],
          isValid: false,
          error: `Waiting for complete header line (got ${headers.length}/6+ columns)`
        };
      }
    }

    // Parse data rows using the same delimiter detection
    if (this.isHeaderParsed && lines.length > 1) {
      const dataLines = lines.slice(1).filter(line => line.trim());
      console.log('Found data lines:', {
        dataLineCount: dataLines.length,
        dataLines: dataLines
      });

      this.rows = dataLines.map((line, lineIndex) => {
        const trimmedLine = line.trim();
        console.log(`Parsing data line ${lineIndex}:`, trimmedLine);

        let cells: string[] = [];

        // Use robust delimiter detection for each row
        if (trimmedLine.includes('|')) {
          const cleanLine = trimmedLine.replace(/^\||\|$/g, '');
          cells = cleanLine.split('|').map(c => c.trim());
        } else if (trimmedLine.includes('\t')) {
          cells = trimmedLine.split('\t').map(c => c.trim());
        } else if (trimmedLine.match(/\s{2,}/)) {
          cells = trimmedLine.split(/\s{2,}/).map(c => c.trim());
        } else {
          cells = [trimmedLine.trim()];
        }

        console.log(`Parsed cells for line ${lineIndex}:`, {
          cellCount: cells.length,
          cells: cells,
          expectedCellCount: this.headers.length
        });

        const row: ParsedTableRow = {};
        this.headers.forEach((header, index) => {
          row[header] = cells[index] || '';
        });

        console.log(`Final row ${lineIndex}:`, row);
        return row;
      });
    }

    const isValid = this.headers.length >= 6 && this.rows.length > 0;
    const result = {
      headers: this.headers,
      rows: this.rows,
      isValid,
      error: isValid ? undefined : `Table incomplete: ${this.headers.length} headers, ${this.rows.length} rows`
    };

    console.log('Final streaming table parse result:', {
      isValid: result.isValid,
      headerCount: result.headers.length,
      rowCount: result.rows.length,
      headers: result.headers,
      firstRow: result.rows[0],
      error: result.error
    });

    return result;
  }

  /**
   * Reset parser state
   */
  reset(): void {
    this.headers = [];
    this.rows = [];
    this.isHeaderParsed = false;
    this.buffer = '';
  }

  /**
   * Check if table is complete (has closing tag)
   */
  isComplete(): boolean {
    return this.buffer.includes('</table_stream>');
  }
}

/**
 * Parse compact streaming table format
 * @param content Table content in streaming format
 * @returns Parsed table object
 */
export const parseStreamingTable = (content: string): ParsedTable => {
  try {
    const lines = content.split('\n').filter(line => line.trim());

    if (lines.length < 2) {
      return { headers: [], rows: [], isValid: false, error: 'Insufficient table data' };
    }

    // Parse headers with multiple format support
    const headerLine = lines[0];
    let headers: string[] = [];

    // Try pipe-delimited format first with proper outer pipe removal
    if (headerLine.includes('|')) {
      const cleanHeader = headerLine.replace(/^\||\|$/g, '');
      headers = cleanHeader.split('|').map(h => h.trim());
      console.log('parseStreamingTable - parsed pipe-delimited headers:', {
        originalLine: headerLine,
        cleanedLine: cleanHeader,
        headers: headers,
        headerCount: headers.length
      });
    }
    // Try tab-delimited format - preserve empty cells
    else if (headerLine.includes('\t')) {
      headers = headerLine.split('\t').map(h => h.trim());
      console.log('parseStreamingTable - parsed tab-delimited headers (preserving empty):', headers);
    }
    // Try space-delimited format (multiple spaces) - preserve structure
    else if (headerLine.includes('  ')) {
      headers = headerLine.split(/\s{2,}/).map(h => h.trim());
      console.log('parseStreamingTable - parsed space-delimited headers (preserving empty):', headers);
    }
    // Fallback: single header
    else {
      headers = [headerLine.trim()];
      console.log('parseStreamingTable - single header fallback:', headers);
    }

    if (headers.length === 0) {
      return { headers: [], rows: [], isValid: false, error: 'No valid headers found' };
    }

    // Parse rows using the same delimiter detection
    const rows: ParsedTableRow[] = lines.slice(1).map(line => {
      let cells: string[] = [];

      // Use the same delimiter as headers with proper outer pipe removal
      if (line.includes('|')) {
        const cleanLine = line.replace(/^\||\|$/g, '');
        cells = cleanLine.split('|').map(c => c.trim());
      } else if (line.includes('\t')) {
        cells = line.split('\t').map(c => c.trim());
      } else if (line.includes('  ')) {
        cells = line.split(/\s{2,}/).map(c => c.trim());
      } else {
        cells = [line.trim()];
      }

      const row: ParsedTableRow = {};
      headers.forEach((header, index) => {
        row[header] = cells[index] || '';
      });
      return row;
    });

    return {
      headers,
      rows,
      isValid: true
    };
  } catch (error) {
    console.error('Error parsing streaming table:', error);
    return {
      headers: [],
      rows: [],
      isValid: false,
      error: `Parsing error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
};

/**
 * Validate and clean table content before parsing
 * @param content The content to validate
 * @returns Cleaned content or null if invalid
 */
export const validateAndCleanTableContent = (content: string): string | null => {
  if (!content || typeof content !== 'string') {
    return null;
  }

  try {
    // Remove common markdown artifacts that might interfere
    let cleaned = content
      .replace(/```[\s\S]*?```/g, '') // Remove code blocks
      .replace(/`[^`]*`/g, '') // Remove inline code
      .replace(/\*\*([^*]*)\*\*/g, '$1') // Remove bold markdown
      .replace(/\*([^*]*)\*/g, '$1') // Remove italic markdown
      .trim();

    // Ensure we have some content left
    if (cleaned.length === 0) {
      return null;
    }

    return cleaned;
  } catch (error) {
    console.error('Error validating table content:', error);
    return null;
  }
};

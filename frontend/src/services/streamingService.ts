/**
 * Streaming Service for handling LLM streaming responses
 * Provides proper error handling and chat switching support
 */

export interface StreamingChunk {
  type: 'content' | 'completion' | 'error';
  content?: string;
  accumulated_content?: string;
  result?: any;
  error?: string;
  function?: string;
  unit_system?: string;
  table_mode?: boolean;
  model?: string;
  message?: string;
}

export interface StreamingOptions {
  text: string;
  unit_system?: string;
  model?: string;
  function?: string;
  sessionId?: string;
  onChunk?: (chunk: StreamingChunk) => void;
  onComplete?: (result: any) => void;
  onError?: (error: string) => void;
  signal?: AbortSignal;
}

class StreamingService {
  private activeStreams = new Map<string, AbortController>();

  /**
   * Start a streaming LLM request
   */
  async startStream(options: StreamingOptions): Promise<void> {
    const {
      text,
      unit_system = 'metric',
      model = 'deepseek-v3-250324',
      function: func = 'general',
      sessionId,
      onChunk,
      onComplete,
      onError,
      signal
    } = options;

    // Create abort controller for this stream
    const abortController = new AbortController();
    const streamId = sessionId || `stream_${Date.now()}`;
    
    // Store the controller so we can abort it later
    this.activeStreams.set(streamId, abortController);

    // If external signal is provided, forward abort to our controller
    if (signal) {
      signal.addEventListener('abort', () => {
        abortController.abort();
      });
    }

    try {
      // Import API_BASE_URL from config
      const { API_BASE_URL } = await import('../config');
      
      // Determine endpoint
      let endpoint = `${API_BASE_URL}/llm/stream`;

      // Handle HTTPS fallback for production servers
      if (window.location.protocol === 'https:' &&
          (window.location.hostname === 'steelnet.ai' ||
           window.location.hostname === '************')) {
        endpoint = `/api/llm/stream`;
      }

      // Prepare request body
      const requestBody = {
        text,
        unit_system,
        model,
        function: func
      };

      // Prepare headers
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Add auth header if user is logged in
      const token = localStorage.getItem('token');
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      console.log('Starting streaming request:', { endpoint, requestBody });

      // Make the streaming request
      const response = await fetch(endpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
        signal: abortController.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      if (!response.body) {
        throw new Error('Response body is null');
      }

      // Process the streaming response with memory optimization
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      const maxBufferSize = 8192; // 8KB buffer limit

      try {
        while (true) {
          const { done, value } = await reader.read();
          
          if (done) {
            break;
          }

          // Check if stream was aborted
          if (abortController.signal.aborted) {
            console.log('Stream aborted');
            break;
          }

          // Decode the chunk
          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;

          // Prevent buffer overflow
          if (buffer.length > maxBufferSize) {
            // Keep only the last portion of the buffer
            buffer = buffer.slice(-maxBufferSize / 2);
          }

          // Process complete lines
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // Keep incomplete line in buffer

          for (const line of lines) {
            if (line.trim() === '') continue;

            // Handle Server-Sent Events format
            if (line.startsWith('data: ')) {
              const data = line.slice(6); // Remove 'data: ' prefix
              
              if (data.trim() === '[DONE]') {
                console.log('Stream completed');
                return;
              }

              try {
                const chunkData: StreamingChunk = JSON.parse(data);
                
                // Call the chunk handler
                if (onChunk) {
                  onChunk(chunkData);
                }

                // Handle completion
                if (chunkData.type === 'completion' && onComplete) {
                  onComplete(chunkData.result);
                }

                // Handle errors
                if (chunkData.type === 'error' && onError) {
                  onError(chunkData.error || 'Unknown streaming error');
                }

              } catch (parseError) {
                console.warn('Failed to parse streaming chunk:', data, parseError);
                // Clear problematic data to prevent memory issues
                buffer = '';
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

    } catch (error) {
      console.error('Streaming error:', error);
      
      // Don't report abort errors as actual errors
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('Stream was aborted');
        return;
      }

      if (onError) {
        onError(error instanceof Error ? error.message : 'Unknown streaming error');
      }
    } finally {
      // Clean up the stream controller
      this.activeStreams.delete(streamId);
    }
  }

  /**
   * Abort a specific stream
   */
  abortStream(sessionId: string): void {
    const controller = this.activeStreams.get(sessionId);
    if (controller) {
      console.log(`Aborting stream for session: ${sessionId}`);
      controller.abort();
      this.activeStreams.delete(sessionId);
    }
  }

  /**
   * Abort all active streams
   */
  abortAllStreams(): void {
    console.log(`Aborting ${this.activeStreams.size} active streams`);
    for (const [sessionId, controller] of this.activeStreams) {
      controller.abort();
    }
    this.activeStreams.clear();
  }

  /**
   * Check if a stream is active for a session
   */
  isStreamActive(sessionId: string): boolean {
    return this.activeStreams.has(sessionId);
  }

  /**
   * Get the number of active streams
   */
  getActiveStreamCount(): number {
    return this.activeStreams.size;
  }
}

// Export singleton instance
export const streamingService = new StreamingService(); 
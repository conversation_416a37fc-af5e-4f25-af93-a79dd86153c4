#!/usr/bin/env python3
"""
Test script to verify table parsing fixes work with the backend
"""

import requests
import json
import time

def test_table_parsing():
    """Test the table parsing functionality with the backend"""
    
    # Test data that should generate a table
    test_input = """
    Please convert the following steel specifications from imperial to metric and create a table:

    001 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 2.343(+/-0.005)" x COIL - 7190#
    002 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 2.406(+/-0.005)" x COIL - 8061#
    003 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 16.50(+/-0.005)" x COIL - 12550#
    004 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 19.68(+/-0.03125)" x COIL - 8835#
    005 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 47.438(+/-0.03125)" x COIL - 57655#
    """
    
    # API endpoint
    url = "http://localhost:8000/llm"
    
    # Request payload
    payload = {
        "text": test_input,
        "unit_system": "metric",
        "function": "table"
    }
    
    print("🧪 Testing Table Parsing with Backend")
    print("=" * 50)
    print(f"Input: {test_input[:100]}...")
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    print()
    
    try:
        # Make the request
        print("📡 Sending request to backend...")
        response = requests.post(url, json=payload, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            print()
            
            # Print the response structure
            print("📋 Response Structure:")
            for key, value in result.items():
                if isinstance(value, str) and len(value) > 100:
                    print(f"  {key}: {value[:100]}...")
                else:
                    print(f"  {key}: {value}")
            print()
            
            # Check for table content
            if 'message' in result:
                message = result['message']
                print("📄 Full Response Message:")
                print("-" * 30)
                print(message)
                print("-" * 30)
                print()
                
                # Check for table_stream format
                if '<table_stream>' in message:
                    print("✅ Found table_stream format!")
                    
                    # Extract table content
                    start = message.find('<table_stream>') + len('<table_stream>')
                    end = message.find('</table_stream>')
                    if end != -1:
                        table_content = message[start:end].strip()
                        print("📊 Extracted Table Content:")
                        print(table_content)
                        print()
                        
                        # Parse the table content
                        lines = table_content.split('\n')
                        if lines:
                            print("🔍 Table Analysis:")
                            print(f"  Total lines: {len(lines)}")
                            
                            if len(lines) > 0:
                                header_line = lines[0]
                                headers = header_line.split('|')
                                print(f"  Headers ({len(headers)}): {headers}")
                                
                                if len(lines) > 1:
                                    print("  Data rows:")
                                    for i, line in enumerate(lines[1:], 1):
                                        cells = line.split('|')
                                        print(f"    Row {i} ({len(cells)} cells): {cells}")
                                        
                                        # Check for column alignment
                                        if len(cells) != len(headers):
                                            print(f"    ⚠️  Column mismatch! Expected {len(headers)}, got {len(cells)}")
                                        else:
                                            print(f"    ✅ Column count matches!")
                    else:
                        print("❌ table_stream tag not properly closed")
                else:
                    print("❌ No table_stream format found in response")
                    
                    # Check for markdown table format
                    if '|' in message and '---' in message:
                        print("📋 Found markdown table format")
                    else:
                        print("❌ No recognizable table format found")
            else:
                print("❌ No 'message' field in response")
                
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - Backend server not running")
        print("💡 Please start the backend server first:")
        print("   cd backend && python3 main.py")
        
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def test_health_endpoint():
    """Test if the backend is running"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is running")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            print(f"Response text: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Backend is not running: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Table Parsing Backend Test")
    print("=" * 50)
    
    # Check if backend is running
    if test_health_endpoint():
        print()
        test_table_parsing()
    else:
        print()
        print("💡 Please start the backend server first:")
        print("   ./start_dev_linux_with_url_access.sh")
        print("   or")
        print("   cd backend && python3 main.py")
